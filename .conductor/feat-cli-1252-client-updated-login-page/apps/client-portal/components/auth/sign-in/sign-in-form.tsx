"use client";

import { useAuth, useSignIn } from "@clerk/nextjs";
import { Eye, EyeOff } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { z } from "zod";

import { Link } from "@/components/shared/link";
import { Button } from "@/components/ui/button";
import { Checkbox, Form, InputField, Label } from "@/components/ui/form";
import { cn } from "@/lib/utils";

// Form validation schema
const signInSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

type SignInFormData = z.infer<typeof signInSchema>;

export function SignInForm() {
  const { isLoaded, signIn, setActive } = useSignIn();
  const { isLoaded: isAuthLoaded, isSignedIn } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (isAuthLoaded && isSignedIn) {
      router.replace("/");
    }
  }, [isAuthLoaded, isSignedIn, router]);

  const onSubmit = async (data: SignInFormData, formHandler: any) => {
    if (!isLoaded) return;

    setIsLoading(true);

    try {
      const result = await signIn.create({
        identifier: data.email,
        password: data.password,
      });

      if (result.status === "complete") {
        await setActive({ session: result.createdSessionId });
        // Redirect will be handled by Clerk's middleware
      } else {
        // Handle other statuses or additional steps if needed
        console.error("Sign-in incomplete:", result.status);
        toast.error("Sign-in process incomplete. Please try again.");
      }
    } catch (error: any) {
      console.error("Sign-in error:", error);

      // Handle specific Clerk errors
      if (error.errors) {
        const clerkError = error.errors[0];
        switch (clerkError.code) {
          case "form_identifier_not_found":
            formHandler.setError("email", {
              message: "Couldn't find your account",
            });
            break;
          case "form_password_incorrect":
            formHandler.setError("password", {
              message: "Password do not match",
            });
            break;
          case "form_identifier_exists":
            toast.error("An account with this email already exists.");
            break;
          case "too_many_requests":
            toast.error("Too many attempts. Please try again later.");
            break;
          default:
            toast.error(
              clerkError.longMessage ||
                clerkError.message ||
                "Sign-in failed. Please try again.",
            );
        }
      } else {
        // Handle network or other errors
        toast.error(
          "Something went wrong. Please check your connection and try again.",
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Show loading state while authentication is being checked
  if (!isAuthLoaded) {
    return (
      <div className="flex w-1/2 flex-col gap-5">
        <div className="flex flex-col items-center justify-center space-y-8 text-center">
          <Image
            src="/images/authentication/light-logo.svg"
            alt="logo"
            width={346}
            height={40}
          />
          <h1 className="text-[32px] font-semibold leading-[150%]">Sign In</h1>
        </div>
        <div className="flex items-center justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-purple-600"></div>
        </div>
      </div>
    );
  }

  // If user is already signed in, show loading while redirect happens
  if (isSignedIn) {
    return (
      <div className="flex w-1/2 flex-col gap-5">
        <div className="flex flex-col items-center justify-center space-y-8 text-center">
          <Image
            src="/images/authentication/light-logo.svg"
            alt="logo"
            width={346}
            height={40}
          />
          <h1 className="text-[32px] font-semibold leading-[150%]">Sign In</h1>
        </div>
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-purple-600"></div>
          <p className="mt-4 text-sm text-gray-600">
            Redirecting to dashboard...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex w-1/2 flex-col gap-5">
      <div className="flex flex-col items-center justify-center space-y-8 text-center">
        <Image
          src="/images/authentication/light-logo.svg"
          alt="logo"
          width={346}
          height={40}
        />
        <h1 className="text-[32px] font-semibold leading-[150%]">Sign In</h1>
      </div>

      <Form
        schema={signInSchema}
        onSubmit={onSubmit}
        defaultValues={{
          email: "",
          password: "",
        }}
        isSubmitting={isLoading}
        mode="onChange"
        formProps={{ shouldFocusError: false }}
        className="flex flex-col gap-5"
      >
        <div>
          <Label htmlFor="email" required>
            Email Address
          </Label>
          <InputField
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            placeholder="Type your email"
          />
        </div>

        <div>
          <Label htmlFor="password" required>
            Password
          </Label>
          <div className="relative">
            <InputField
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              autoComplete="current-password"
              placeholder="Your password"
              className="pr-10"
            />
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className={cn(
                "absolute right-0 top-3 flex items-center pr-3",
                "text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100",
                "disabled:cursor-not-allowed disabled:opacity-50",
              )}
              aria-label={showPassword ? "Hide password" : "Show password"}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 scale-x-[-1]" aria-hidden="true" />
              ) : (
                <Eye className="h-5 w-5" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        <div className="flex justify-between pb-5">
          <div className="flex items-center gap-2">
            <Checkbox id="remember-me" name="remember-me" />
            <Label
              htmlFor="remember-me"
              className="text-sm font-medium leading-5"
            >
              Keep me signed in
            </Label>
          </div>
          <Link
            href="/authentication/password-reset"
            className="text-sm font-medium leading-5 underline-offset-4 hover:underline"
          >
            Forgot password?
          </Link>
        </div>

        <Button
          type="submit"
          isLoading={isLoading}
          disabled={!isLoaded}
          disabledForInvalid
          className="w-full"
        >
          {isLoading ? "Logging in..." : "Login"}
        </Button>
      </Form>

      <div className="mt-6 text-center text-sm">
        <span>Need help? </span>
        <a
          href="mailto:<EMAIL>"
          className="text-purple-600 underline-offset-4 hover:text-purple-700 hover:underline"
        >
          Contact support
        </a>
      </div>
    </div>
  );
}
